import '../entities/policy.dart';
import '../repositories/insurance_repository.dart';

class ApplyForInsuranceParams {
  final String providerId;
  final String userId;
  final PolicyType type;
  final Map<String, dynamic> applicationData;

  const ApplyForInsuranceParams({
    required this.providerId,
    required this.userId,
    required this.type,
    required this.applicationData,
  });
}

class ApplyForInsurance {
  final InsuranceRepository repository;

  ApplyForInsurance(this.repository);

  Future<String> call(ApplyForInsuranceParams params) async {
    return await repository.submitInsuranceApplication(
      providerId: params.providerId,
      userId: params.userId,
      type: params.type,
      applicationData: params.applicationData,
    );
  }
}

class GetApplicationStatus {
  final InsuranceRepository repository;

  GetApplicationStatus(this.repository);

  Future<Map<String, dynamic>> call(String applicationId) async {
    return await repository.getApplicationStatus(applicationId);
  }
}

class CreatePolicyParams {
  final String userId;
  final String providerId;
  final String providerName;
  final PolicyType type;
  final String title;
  final String description;
  final List<PolicyCoverage> coverages;
  final double premiumAmount;
  final PaymentFrequency paymentFrequency;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> terms;
  final String? vehicleId;
  final String? propertyId;
  final bool autoRenewal;

  const CreatePolicyParams({
    required this.userId,
    required this.providerId,
    required this.providerName,
    required this.type,
    required this.title,
    required this.description,
    required this.coverages,
    required this.premiumAmount,
    required this.paymentFrequency,
    required this.startDate,
    required this.endDate,
    required this.terms,
    this.vehicleId,
    this.propertyId,
    this.autoRenewal = false,
  });
}

class CreatePolicy {
  final InsuranceRepository repository;

  CreatePolicy(this.repository);

  Future<Policy> call(CreatePolicyParams params) async {
    final totalCoverageAmount = params.coverages.fold<double>(
      0.0,
      (sum, coverage) => sum + coverage.coverageAmount,
    );

    final policy = Policy(
      id: '', // Will be generated by the repository
      userId: params.userId,
      providerId: params.providerId,
      providerName: params.providerName,
      policyNumber: '', // Will be generated by the repository
      type: params.type,
      status: PolicyStatus.pending,
      title: params.title,
      description: params.description,
      coverages: params.coverages,
      totalCoverageAmount: totalCoverageAmount,
      premiumAmount: params.premiumAmount,
      paymentFrequency: params.paymentFrequency,
      currency: 'TZS', // Default currency
      startDate: params.startDate,
      endDate: params.endDate,
      beneficiaries: const [],
      payments: const [],
      terms: params.terms,
      documents: const [],
      vehicleId: params.vehicleId,
      propertyId: params.propertyId,
      autoRenewal: params.autoRenewal,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createPolicy(policy);
  }
}

class UploadDocumentParams {
  final String entityId;
  final String entityType;
  final String fileName;
  final List<int> fileBytes;

  const UploadDocumentParams({
    required this.entityId,
    required this.entityType,
    required this.fileName,
    required this.fileBytes,
  });
}

class UploadDocument {
  final InsuranceRepository repository;

  UploadDocument(this.repository);

  Future<String> call(UploadDocumentParams params) async {
    return await repository.uploadDocument(
      entityId: params.entityId,
      entityType: params.entityType,
      fileName: params.fileName,
      fileBytes: params.fileBytes,
    );
  }
}

class DeleteDocument {
  final InsuranceRepository repository;

  DeleteDocument(this.repository);

  Future<void> call(String documentId) async {
    return await repository.deleteDocument(documentId);
  }
}
