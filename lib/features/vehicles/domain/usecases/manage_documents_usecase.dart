import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/vehicle_repository.dart';
import '../entities/document.dart';

/// Get Vehicle Documents Use Case
/// Following specifications from FEATURES_DOCUMENTATION.md - Vehicle Management Feature
class GetVehicleDocumentsUseCase implements UseCase<List<Document>, String> {
  final VehicleRepository repository;

  GetVehicleDocumentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Document>>> call(String vehicleId) async {
    return await repository.getVehicleDocuments(vehicleId);
  }
}

/// Get User Documents Use Case
class GetUserDocumentsUseCase implements UseCase<List<Document>, String> {
  final VehicleRepository repository;

  GetUserDocumentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Document>>> call(String userId) async {
    return await repository.getUserDocuments(userId);
  }
}

/// Create Document Use Case
class CreateDocumentUseCase implements UseCase<Document, CreateDocumentParams> {
  final VehicleRepository repository;

  CreateDocumentUseCase(this.repository);

  @override
  Future<Either<Failure, Document>> call(CreateDocumentParams params) async {
    // Validate required fields
    if (params.title.trim().isEmpty) {
      return const Left(ValidationFailure('Document title is required'));
    }
    if (params.fileUrl.trim().isEmpty) {
      return const Left(ValidationFailure('Document file is required'));
    }

    // Validate expiry date for documents that require it
    if (params.type.hasExpiryDate && params.expiryDate == null) {
      return const Left(
        ValidationFailure('Expiry date is required for this document type'),
      );
    }

    // Create document entity
    final document = Document(
      id: '', // Will be generated by repository
      vehicleId: params.vehicleId,
      userId: params.userId,
      type: params.type,
      title: params.title.trim(),
      description: params.description?.trim(),
      fileUrl: params.fileUrl,
      fileName: params.fileName,
      fileType: params.fileType,
      fileSize: params.fileSize,
      issueDate: params.issueDate,
      expiryDate: params.expiryDate,
      issuer: params.issuer?.trim(),
      documentNumber: params.documentNumber?.trim(),
      status: DocumentStatus.active,
      metadata: params.metadata,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createDocument(document);
  }
}

/// Update Document Use Case
class UpdateDocumentUseCase implements UseCase<Document, UpdateDocumentParams> {
  final VehicleRepository repository;

  UpdateDocumentUseCase(this.repository);

  @override
  Future<Either<Failure, Document>> call(UpdateDocumentParams params) async {
    // Get existing document
    final existingDocumentResult = await repository.getDocumentById(
      params.documentId,
    );

    return existingDocumentResult.fold((failure) => Left(failure), (
      existingDocument,
    ) async {
      if (existingDocument == null) {
        return const Left(NotFoundFailure('Document not found'));
      }

      // Update document with new data
      final updatedDocument = existingDocument.copyWith(
        title: params.title?.trim(),
        description: params.description?.trim(),
        fileUrl: params.fileUrl,
        fileName: params.fileName,
        fileType: params.fileType,
        fileSize: params.fileSize,
        issueDate: params.issueDate,
        expiryDate: params.expiryDate,
        issuer: params.issuer?.trim(),
        documentNumber: params.documentNumber?.trim(),
        status: params.status,
        metadata: params.metadata,
        updatedAt: DateTime.now(),
      );

      return await repository.updateDocument(updatedDocument);
    });
  }
}

/// Delete Document Use Case
class DeleteDocumentUseCase implements UseCase<void, String> {
  final VehicleRepository repository;

  DeleteDocumentUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(String documentId) async {
    return await repository.deleteDocument(documentId);
  }
}

/// Get Expired Documents Use Case
class GetExpiredDocumentsUseCase implements UseCase<List<Document>, String> {
  final VehicleRepository repository;

  GetExpiredDocumentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Document>>> call(String userId) async {
    return await repository.getExpiredDocuments(userId);
  }
}

/// Get Expiring Soon Documents Use Case
class GetExpiringSoonDocumentsUseCase
    implements UseCase<List<Document>, String> {
  final VehicleRepository repository;

  GetExpiringSoonDocumentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Document>>> call(String userId) async {
    return await repository.getExpiringSoonDocuments(userId);
  }
}

/// Upload Document Use Case
class UploadDocumentUseCase implements UseCase<String, UploadDocumentParams> {
  final VehicleRepository repository;

  UploadDocumentUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(UploadDocumentParams params) async {
    return await repository.uploadDocument(params.documentId, params.filePath);
  }
}

/// Parameters for creating a document
class CreateDocumentParams {
  final String vehicleId;
  final String userId;
  final DocumentType type;
  final String title;
  final String? description;
  final String fileUrl;
  final String fileName;
  final String fileType;
  final int fileSize;
  final DateTime? issueDate;
  final DateTime? expiryDate;
  final String? issuer;
  final String? documentNumber;
  final Map<String, dynamic> metadata;

  CreateDocumentParams({
    required this.vehicleId,
    required this.userId,
    required this.type,
    required this.title,
    this.description,
    required this.fileUrl,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    this.issueDate,
    this.expiryDate,
    this.issuer,
    this.documentNumber,
    this.metadata = const {},
  });
}

/// Parameters for updating a document
class UpdateDocumentParams {
  final String documentId;
  final String? title;
  final String? description;
  final String? fileUrl;
  final String? fileName;
  final String? fileType;
  final int? fileSize;
  final DateTime? issueDate;
  final DateTime? expiryDate;
  final String? issuer;
  final String? documentNumber;
  final DocumentStatus? status;
  final Map<String, dynamic>? metadata;

  UpdateDocumentParams({
    required this.documentId,
    this.title,
    this.description,
    this.fileUrl,
    this.fileName,
    this.fileType,
    this.fileSize,
    this.issueDate,
    this.expiryDate,
    this.issuer,
    this.documentNumber,
    this.status,
    this.metadata,
  });
}

/// Parameters for uploading a document
class UploadDocumentParams {
  final String documentId;
  final String filePath;

  UploadDocumentParams({required this.documentId, required this.filePath});
}
