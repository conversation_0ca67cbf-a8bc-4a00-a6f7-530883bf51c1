import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/vehicle_repository.dart';
import '../entities/maintenance_record.dart';

/// Get Vehicle Maintenance Records Use Case
/// Following specifications from FEATURES_DOCUMENTATION.md - Vehicle Management Feature
class GetVehicleMaintenanceRecordsUseCase implements UseCase<List<MaintenanceRecord>, String> {
  final VehicleRepository repository;

  GetVehicleMaintenanceRecordsUseCase(this.repository);

  @override
  Future<Either<Failure, List<MaintenanceRecord>>> call(String vehicleId) async {
    return await repository.getVehicleMaintenanceRecords(vehicleId);
  }
}

/// Get User Maintenance Records Use Case
class GetUserMaintenanceRecordsUseCase implements UseCase<List<MaintenanceRecord>, String> {
  final VehicleRepository repository;

  GetUserMaintenanceRecordsUseCase(this.repository);

  @override
  Future<Either<Failure, List<MaintenanceRecord>>> call(String userId) async {
    return await repository.getUserMaintenanceRecords(userId);
  }
}

/// Create Maintenance Record Use Case
class CreateMaintenanceRecordUseCase implements UseCase<MaintenanceRecord, CreateMaintenanceRecordParams> {
  final VehicleRepository repository;

  CreateMaintenanceRecordUseCase(this.repository);

  @override
  Future<Either<Failure, MaintenanceRecord>> call(CreateMaintenanceRecordParams params) async {
    // Validate required fields
    if (params.title.trim().isEmpty) {
      return const Left(ValidationFailure('Maintenance title is required'));
    }
    if (params.description.trim().isEmpty) {
      return const Left(ValidationFailure('Maintenance description is required'));
    }
    if (params.cost < 0) {
      return const Left(ValidationFailure('Cost cannot be negative'));
    }

    // Create maintenance record entity
    final record = MaintenanceRecord(
      id: '', // Will be generated by repository
      vehicleId: params.vehicleId,
      userId: params.userId,
      type: params.type,
      title: params.title.trim(),
      description: params.description.trim(),
      serviceDate: params.serviceDate,
      mileageAtService: params.mileageAtService,
      cost: params.cost,
      currency: params.currency,
      serviceProvider: params.serviceProvider?.trim(),
      serviceProviderContact: params.serviceProviderContact?.trim(),
      location: params.location?.trim(),
      partsReplaced: params.partsReplaced,
      servicesPerformed: params.servicesPerformed,
      nextServiceDate: params.nextServiceDate,
      nextServiceMileage: params.nextServiceMileage,
      receiptUrls: params.receiptUrls,
      notes: params.notes,
      status: params.status,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createMaintenanceRecord(record);
  }
}

/// Update Maintenance Record Use Case
class UpdateMaintenanceRecordUseCase implements UseCase<MaintenanceRecord, UpdateMaintenanceRecordParams> {
  final VehicleRepository repository;

  UpdateMaintenanceRecordUseCase(this.repository);

  @override
  Future<Either<Failure, MaintenanceRecord>> call(UpdateMaintenanceRecordParams params) async {
    // Get existing maintenance record
    final existingRecordResult = await repository.getMaintenanceRecordById(params.recordId);
    
    return existingRecordResult.fold(
      (failure) => Left(failure),
      (existingRecord) async {
        if (existingRecord == null) {
          return const Left(NotFoundFailure('Maintenance record not found'));
        }

        // Update maintenance record with new data
        final updatedRecord = existingRecord.copyWith(
          type: params.type,
          title: params.title?.trim(),
          description: params.description?.trim(),
          serviceDate: params.serviceDate,
          mileageAtService: params.mileageAtService,
          cost: params.cost,
          currency: params.currency,
          serviceProvider: params.serviceProvider?.trim(),
          serviceProviderContact: params.serviceProviderContact?.trim(),
          location: params.location?.trim(),
          partsReplaced: params.partsReplaced,
          servicesPerformed: params.servicesPerformed,
          nextServiceDate: params.nextServiceDate,
          nextServiceMileage: params.nextServiceMileage,
          receiptUrls: params.receiptUrls,
          notes: params.notes,
          status: params.status,
          updatedAt: DateTime.now(),
        );

        return await repository.updateMaintenanceRecord(updatedRecord);
      },
    );
  }
}

/// Delete Maintenance Record Use Case
class DeleteMaintenanceRecordUseCase implements UseCase<void, String> {
  final VehicleRepository repository;

  DeleteMaintenanceRecordUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(String recordId) async {
    return await repository.deleteMaintenanceRecord(recordId);
  }
}

/// Get Overdue Maintenance Records Use Case
class GetOverdueMaintenanceRecordsUseCase implements UseCase<List<MaintenanceRecord>, String> {
  final VehicleRepository repository;

  GetOverdueMaintenanceRecordsUseCase(this.repository);

  @override
  Future<Either<Failure, List<MaintenanceRecord>>> call(String userId) async {
    return await repository.getOverdueMaintenanceRecords(userId);
  }
}

/// Get Total Maintenance Cost Use Case
class GetTotalMaintenanceCostUseCase implements UseCase<double, GetMaintenanceCostParams> {
  final VehicleRepository repository;

  GetTotalMaintenanceCostUseCase(this.repository);

  @override
  Future<Either<Failure, double>> call(GetMaintenanceCostParams params) async {
    return await repository.getTotalMaintenanceCost(
      params.vehicleId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Get Maintenance Statistics Use Case
class GetMaintenanceStatisticsUseCase implements UseCase<Map<String, dynamic>, String> {
  final VehicleRepository repository;

  GetMaintenanceStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(String vehicleId) async {
    return await repository.getMaintenanceStatistics(vehicleId);
  }
}

/// Parameters for creating a maintenance record
class CreateMaintenanceRecordParams {
  final String vehicleId;
  final String userId;
  final MaintenanceType type;
  final String title;
  final String description;
  final DateTime serviceDate;
  final int? mileageAtService;
  final double cost;
  final String currency;
  final String? serviceProvider;
  final String? serviceProviderContact;
  final String? location;
  final List<String> partsReplaced;
  final List<String> servicesPerformed;
  final DateTime? nextServiceDate;
  final int? nextServiceMileage;
  final List<String> receiptUrls;
  final Map<String, dynamic> notes;
  final MaintenanceStatus status;

  CreateMaintenanceRecordParams({
    required this.vehicleId,
    required this.userId,
    required this.type,
    required this.title,
    required this.description,
    required this.serviceDate,
    this.mileageAtService,
    required this.cost,
    this.currency = 'TZS',
    this.serviceProvider,
    this.serviceProviderContact,
    this.location,
    this.partsReplaced = const [],
    this.servicesPerformed = const [],
    this.nextServiceDate,
    this.nextServiceMileage,
    this.receiptUrls = const [],
    this.notes = const {},
    this.status = MaintenanceStatus.completed,
  });
}

/// Parameters for updating a maintenance record
class UpdateMaintenanceRecordParams {
  final String recordId;
  final MaintenanceType? type;
  final String? title;
  final String? description;
  final DateTime? serviceDate;
  final int? mileageAtService;
  final double? cost;
  final String? currency;
  final String? serviceProvider;
  final String? serviceProviderContact;
  final String? location;
  final List<String>? partsReplaced;
  final List<String>? servicesPerformed;
  final DateTime? nextServiceDate;
  final int? nextServiceMileage;
  final List<String>? receiptUrls;
  final Map<String, dynamic>? notes;
  final MaintenanceStatus? status;

  UpdateMaintenanceRecordParams({
    required this.recordId,
    this.type,
    this.title,
    this.description,
    this.serviceDate,
    this.mileageAtService,
    this.cost,
    this.currency,
    this.serviceProvider,
    this.serviceProviderContact,
    this.location,
    this.partsReplaced,
    this.servicesPerformed,
    this.nextServiceDate,
    this.nextServiceMileage,
    this.receiptUrls,
    this.notes,
    this.status,
  });
}

/// Parameters for getting maintenance cost
class GetMaintenanceCostParams {
  final String vehicleId;
  final DateTime? startDate;
  final DateTime? endDate;

  GetMaintenanceCostParams({
    required this.vehicleId,
    this.startDate,
    this.endDate,
  });
}
