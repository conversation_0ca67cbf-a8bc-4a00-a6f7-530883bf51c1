import '../entities/return.dart';
import '../repositories/orders_repository.dart';

class RequestReturnParams {
  final String orderId;
  final String userId;
  final List<ReturnItem> items;
  final RefundMethod refundMethod;
  final String? customerNotes;
  final List<String> images;

  const RequestReturnParams({
    required this.orderId,
    required this.userId,
    required this.items,
    required this.refundMethod,
    this.customerNotes,
    this.images = const [],
  });

  bool get isValid => items.isNotEmpty;
}

class RequestReturn {
  final OrdersRepository repository;

  RequestReturn(this.repository);

  Future<Return> call(RequestReturnParams params) async {
    if (!params.isValid) {
      throw ArgumentError('At least one item must be selected for return');
    }

    final totalRefundAmount = params.items.fold<double>(
      0.0,
      (sum, item) => sum + item.refundAmount,
    );

    final returnRequest = Return(
      id: '', // Will be generated by the repository
      orderId: params.orderId,
      userId: params.userId,
      returnNumber: '', // Will be generated by the repository
      status: ReturnStatus.requested,
      items: params.items,
      totalRefundAmount: totalRefundAmount,
      refundMethod: params.refundMethod,
      currency: 'TZS', // Default currency
      customerNotes: params.customerNotes,
      images: params.images,
      requestDate: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createReturn(returnRequest);
  }
}

class ViewReturns {
  final OrdersRepository repository;

  ViewReturns(this.repository);

  Future<List<Return>> call(String userId, {int page = 1, int limit = 20}) async {
    return await repository.getUserReturns(userId, page: page, limit: limit);
  }
}

class GetReturnDetails {
  final OrdersRepository repository;

  GetReturnDetails(this.repository);

  Future<Return?> call(String returnId) async {
    return await repository.getReturnById(returnId);
  }
}

class CancelReturn {
  final OrdersRepository repository;

  CancelReturn(this.repository);

  Future<void> call(String returnId) async {
    return await repository.cancelReturn(returnId);
  }
}
