import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/latra_application.dart';
import '../repositories/latra_repository.dart';

/// Register with LATRA use case
/// Following specifications from FEATURES_DOCUMENTATION.md - LATRA Integration Feature
class RegisterWithLATRAUseCase
    implements UseCase<LATRAApplication, RegisterWithLATRAParams> {
  final LATRARepository repository;

  const RegisterWithLATRAUseCase(this.repository);

  @override
  Future<Either<Failure, LATRAApplication>> call(
    RegisterWithLATRAParams params,
  ) async {
    // Validate required parameters
    if (params.userId.isEmpty) {
      return const Left(ValidationFailure('User ID is required'));
    }

    if (params.vehicleId.isEmpty) {
      return const Left(ValidationFailure('Vehicle ID is required'));
    }

    if (params.formData.isEmpty) {
      return const Left(ValidationFailure('Form data is required'));
    }

    // Get required documents for the application type
    final requiredDocumentsResult = await repository.getRequiredDocuments(
      params.type,
    );
    if (requiredDocumentsResult.isLeft()) {
      return Left(
        requiredDocumentsResult.fold((l) => l, (r) => throw Exception()),
      );
    }

    final requiredDocuments = requiredDocumentsResult.fold(
      (l) => <String>[],
      (r) => r,
    );

    // Get application fee
    final feeResult = await repository.getApplicationFee(params.type);
    if (feeResult.isLeft()) {
      return Left(feeResult.fold((l) => l, (r) => throw Exception()));
    }

    final applicationFee = feeResult.fold((l) => 0.0, (r) => r);

    // Create application
    final application = LATRAApplication(
      id: '', // Will be generated by repository
      userId: params.userId,
      vehicleId: params.vehicleId,
      applicationNumber: '', // Will be generated by repository
      type: params.type,
      status: LATRAApplicationStatus.draft,
      title: params.type.displayName,
      description:
          params.description ?? 'LATRA ${params.type.displayName} application',
      formData: params.formData,
      requiredDocuments: requiredDocuments,
      applicationFee: applicationFee,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Create the application
    final createResult = await repository.createApplication(application);
    if (createResult.isLeft()) {
      return createResult;
    }

    final createdApplication = createResult.fold(
      (l) => throw Exception(),
      (r) => r,
    );

    // If auto-submit is enabled, submit the application
    if (params.autoSubmit) {
      final submitResult = await repository.submitApplication(
        createdApplication.id,
      );
      if (submitResult.isLeft()) {
        // Return the created application even if submission fails
        return Right(createdApplication);
      }

      // Return the updated application after submission
      final updatedResult = await repository.getApplicationById(
        createdApplication.id,
      );
      return updatedResult.fold(
        (failure) =>
            Right(createdApplication), // Fallback to created application
        (updatedApp) => Right(updatedApp ?? createdApplication),
      );
    }

    return Right(createdApplication);
  }
}

/// Parameters for Register with LATRA use case
class RegisterWithLATRAParams extends Equatable {
  final String userId;
  final String vehicleId;
  final LATRAApplicationType type;
  final Map<String, dynamic> formData;
  final String? description;
  final bool autoSubmit;

  const RegisterWithLATRAParams({
    required this.userId,
    required this.vehicleId,
    required this.type,
    required this.formData,
    this.description,
    this.autoSubmit = false,
  });

  @override
  List<Object?> get props => [
    userId,
    vehicleId,
    type,
    formData,
    description,
    autoSubmit,
  ];
}
