import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/campaign.dart';
import '../entities/notification.dart';
import '../repositories/campaigns_repository.dart';

class GetCampaignsParams {
  final int page;
  final int limit;
  final CampaignStatus? status;
  final CampaignType? type;

  const GetCampaignsParams({
    this.page = 1,
    this.limit = 20,
    this.status,
    this.type,
  });
}

class GetCampaigns implements UseCase<List<Campaign>, GetCampaignsParams> {
  final CampaignsRepository repository;

  GetCampaigns(this.repository);

  @override
  Future<Either<Failure, List<Campaign>>> call(
    GetCampaignsParams params,
  ) async {
    try {
      final campaigns = await repository.getCampaigns(
        page: params.page,
        limit: params.limit,
        status: params.status,
        type: params.type,
      );
      return Right(campaigns);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class CreateCampaignParams {
  final String name;
  final String description;
  final CampaignType type;
  final String templateId;
  final CampaignTarget target;
  final CampaignSchedule schedule;
  final List<NotificationChannel> channels;
  final Map<String, dynamic>? content;
  final String createdBy;

  const CreateCampaignParams({
    required this.name,
    required this.description,
    required this.type,
    required this.templateId,
    required this.target,
    required this.schedule,
    required this.channels,
    this.content,
    required this.createdBy,
  });
}

class CreateCampaign implements UseCase<Campaign, CreateCampaignParams> {
  final CampaignsRepository repository;

  CreateCampaign(this.repository);

  @override
  Future<Either<Failure, Campaign>> call(CreateCampaignParams params) async {
    try {
      final campaign = Campaign(
        id: '', // Will be generated by the repository
        name: params.name,
        description: params.description,
        type: params.type,
        status: CampaignStatus.draft,
        templateId: params.templateId,
        target: params.target,
        schedule: params.schedule,
        channels: params.channels,
        content: params.content,
        createdBy: params.createdBy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final createdCampaign = await repository.createCampaign(campaign);
      return Right(createdCampaign);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class LaunchCampaignParams {
  final String campaignId;

  const LaunchCampaignParams({required this.campaignId});
}

class LaunchCampaign implements UseCase<Campaign, LaunchCampaignParams> {
  final CampaignsRepository repository;

  LaunchCampaign(this.repository);

  @override
  Future<Either<Failure, Campaign>> call(LaunchCampaignParams params) async {
    try {
      final campaign = await repository.launchCampaign(params.campaignId);
      return Right(campaign);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class PauseCampaignParams {
  final String campaignId;

  const PauseCampaignParams({required this.campaignId});
}

class PauseCampaign implements UseCase<Campaign, PauseCampaignParams> {
  final CampaignsRepository repository;

  PauseCampaign(this.repository);

  @override
  Future<Either<Failure, Campaign>> call(PauseCampaignParams params) async {
    try {
      final campaign = await repository.pauseCampaign(params.campaignId);
      return Right(campaign);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class GetCampaignStatsParams {
  final String campaignId;

  const GetCampaignStatsParams({required this.campaignId});
}

class GetCampaignStats
    implements UseCase<CampaignStats, GetCampaignStatsParams> {
  final CampaignsRepository repository;

  GetCampaignStats(this.repository);

  @override
  Future<Either<Failure, CampaignStats>> call(
    GetCampaignStatsParams params,
  ) async {
    try {
      final stats = await repository.getCampaignStats(params.campaignId);
      return Right(stats);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class EstimateAudienceParams {
  final CampaignTarget target;

  const EstimateAudienceParams({required this.target});
}

class EstimateAudience implements UseCase<int, EstimateAudienceParams> {
  final CampaignsRepository repository;

  EstimateAudience(this.repository);

  @override
  Future<Either<Failure, int>> call(EstimateAudienceParams params) async {
    try {
      final estimate = await repository.estimateAudience(params.target);
      return Right(estimate);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class UpdateCampaignParams {
  final Campaign campaign;

  const UpdateCampaignParams({required this.campaign});
}

class UpdateCampaign implements UseCase<Campaign, UpdateCampaignParams> {
  final CampaignsRepository repository;

  UpdateCampaign(this.repository);

  @override
  Future<Either<Failure, Campaign>> call(UpdateCampaignParams params) async {
    try {
      final updatedCampaign = await repository.updateCampaign(
        params.campaign.copyWith(updatedAt: DateTime.now()),
      );
      return Right(updatedCampaign);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class DeleteCampaignParams {
  final String campaignId;

  const DeleteCampaignParams({required this.campaignId});
}

class DeleteCampaign implements UseCase<void, DeleteCampaignParams> {
  final CampaignsRepository repository;

  DeleteCampaign(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteCampaignParams params) async {
    try {
      await repository.deleteCampaign(params.campaignId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
