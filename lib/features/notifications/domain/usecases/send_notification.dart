import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/notification.dart';
import '../repositories/notifications_repository.dart';

class SendNotificationParams {
  final String userId;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final List<NotificationChannel> channels;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final String? actionText;
  final DateTime? scheduledAt;
  final String? campaignId;
  final String? templateId;

  const SendNotificationParams({
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    required this.channels,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.actionText,
    this.scheduledAt,
    this.campaignId,
    this.templateId,
  });
}

class SendNotification
    implements UseCase<Notification, SendNotificationParams> {
  final NotificationsRepository repository;

  SendNotification(this.repository);

  @override
  Future<Either<Failure, Notification>> call(
    SendNotificationParams params,
  ) async {
    try {
      final notification = Notification(
        id: '', // Will be generated by the repository
        userId: params.userId,
        title: params.title,
        body: params.body,
        type: params.type,
        priority: params.priority,
        status: params.scheduledAt != null
            ? NotificationStatus.pending
            : NotificationStatus.sent,
        channels: params.channels,
        data: params.data,
        imageUrl: params.imageUrl,
        actionUrl: params.actionUrl,
        actionText: params.actionText,
        scheduledAt: params.scheduledAt,
        campaignId: params.campaignId,
        templateId: params.templateId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final createdNotification = await repository.createNotification(
        notification,
      );

      // If not scheduled, send immediately
      if (params.scheduledAt == null) {
        await repository.sendNotification(createdNotification.id);
      } else {
        await repository.scheduleNotification(
          createdNotification.id,
          params.scheduledAt!,
        );
      }

      return Right(createdNotification);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class SendBulkNotificationParams {
  final List<String> userIds;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final List<NotificationChannel> channels;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final String? actionText;
  final DateTime? scheduledAt;
  final String? campaignId;
  final String? templateId;

  const SendBulkNotificationParams({
    required this.userIds,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    required this.channels,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.actionText,
    this.scheduledAt,
    this.campaignId,
    this.templateId,
  });
}

class SendBulkNotification
    implements UseCase<List<Notification>, SendBulkNotificationParams> {
  final NotificationsRepository repository;

  SendBulkNotification(this.repository);

  @override
  Future<Either<Failure, List<Notification>>> call(
    SendBulkNotificationParams params,
  ) async {
    try {
      final notifications = <Notification>[];
      final notificationIds = <String>[];

      // Create notifications for each user
      for (final userId in params.userIds) {
        final notification = Notification(
          id: '', // Will be generated by the repository
          userId: userId,
          title: params.title,
          body: params.body,
          type: params.type,
          priority: params.priority,
          status: params.scheduledAt != null
              ? NotificationStatus.pending
              : NotificationStatus.sent,
          channels: params.channels,
          data: params.data,
          imageUrl: params.imageUrl,
          actionUrl: params.actionUrl,
          actionText: params.actionText,
          scheduledAt: params.scheduledAt,
          campaignId: params.campaignId,
          templateId: params.templateId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final createdNotification = await repository.createNotification(
          notification,
        );
        notifications.add(createdNotification);
        notificationIds.add(createdNotification.id);
      }

      // Send all notifications at once
      if (params.scheduledAt == null) {
        await repository.sendBulkNotifications(notificationIds);
      } else {
        // Schedule each notification individually
        for (final notificationId in notificationIds) {
          await repository.scheduleNotification(
            notificationId,
            params.scheduledAt!,
          );
        }
      }

      return Right(notifications);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
