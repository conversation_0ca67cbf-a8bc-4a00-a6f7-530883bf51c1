import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/template.dart';
import '../repositories/templates_repository.dart';

class GetTemplatesParams {
  final int page;
  final int limit;
  final TemplateType? type;
  final TemplateCategory? category;
  final bool? isActive;

  const GetTemplatesParams({
    this.page = 1,
    this.limit = 20,
    this.type,
    this.category,
    this.isActive,
  });
}

class GetTemplates implements UseCase<List<Template>, GetTemplatesParams> {
  final TemplatesRepository repository;

  GetTemplates(this.repository);

  @override
  Future<Either<Failure, List<Template>>> call(
    GetTemplatesParams params,
  ) async {
    try {
      final templates = await repository.getTemplates(
        page: params.page,
        limit: params.limit,
        type: params.type,
        category: params.category,
        isActive: params.isActive,
      );
      return Right(templates);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class CreateTemplateParams {
  final String name;
  final String description;
  final TemplateType type;
  final TemplateCategory category;
  final TemplateContent content;
  final List<TemplateVariable> variables;
  final bool isActive;
  final bool isDefault;
  final String? previewImageUrl;
  final Map<String, dynamic>? metadata;
  final String createdBy;

  const CreateTemplateParams({
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    required this.content,
    required this.variables,
    this.isActive = true,
    this.isDefault = false,
    this.previewImageUrl,
    this.metadata,
    required this.createdBy,
  });
}

class CreateTemplate implements UseCase<Template, CreateTemplateParams> {
  final TemplatesRepository repository;

  CreateTemplate(this.repository);

  @override
  Future<Either<Failure, Template>> call(CreateTemplateParams params) async {
    try {
      final template = Template(
        id: '', // Will be generated by the repository
        name: params.name,
        description: params.description,
        type: params.type,
        category: params.category,
        content: params.content,
        variables: params.variables,
        isActive: params.isActive,
        isDefault: params.isDefault,
        previewImageUrl: params.previewImageUrl,
        metadata: params.metadata,
        createdBy: params.createdBy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        usageCount: 0,
      );

      final createdTemplate = await repository.createTemplate(template);
      return Right(createdTemplate);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class UpdateTemplateParams {
  final Template template;

  const UpdateTemplateParams({required this.template});
}

class UpdateTemplate implements UseCase<Template, UpdateTemplateParams> {
  final TemplatesRepository repository;

  UpdateTemplate(this.repository);

  @override
  Future<Either<Failure, Template>> call(UpdateTemplateParams params) async {
    try {
      final updatedTemplate = await repository.updateTemplate(
        params.template.copyWith(updatedAt: DateTime.now()),
      );
      return Right(updatedTemplate);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class DeleteTemplateParams {
  final String templateId;

  const DeleteTemplateParams({required this.templateId});
}

class DeleteTemplate implements UseCase<void, DeleteTemplateParams> {
  final TemplatesRepository repository;

  DeleteTemplate(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteTemplateParams params) async {
    try {
      await repository.deleteTemplate(params.templateId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class ImportTemplateParams {
  final String templateId;

  const ImportTemplateParams({required this.templateId});
}

class ImportTemplate implements UseCase<Template, ImportTemplateParams> {
  final TemplatesRepository repository;

  ImportTemplate(this.repository);

  @override
  Future<Either<Failure, Template>> call(ImportTemplateParams params) async {
    try {
      final template = await repository.importTemplate({
        'templateId': params.templateId,
      });
      return Right(template);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class ExportTemplateParams {
  final String templateId;

  const ExportTemplateParams({required this.templateId});
}

class ExportTemplate implements UseCase<void, ExportTemplateParams> {
  final TemplatesRepository repository;

  ExportTemplate(this.repository);

  @override
  Future<Either<Failure, void>> call(ExportTemplateParams params) async {
    try {
      await repository.exportTemplate(params.templateId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class WatchTemplatesParams {
  final String templateId;

  const WatchTemplatesParams({required this.templateId});
}

class WatchTemplates implements UseCase<void, WatchTemplatesParams> {
  final TemplatesRepository repository;

  WatchTemplates(this.repository);

  @override
  Future<Either<Failure, void>> call(WatchTemplatesParams params) async {
    try {
      await repository.watchTemplate(params.templateId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class PreviewTemplateParams {
  final String templateId;
  final Map<String, dynamic> data;

  const PreviewTemplateParams({required this.templateId, required this.data});
}

class PreviewTemplate
    implements UseCase<Map<String, dynamic>, PreviewTemplateParams> {
  final TemplatesRepository repository;

  PreviewTemplate(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(
    PreviewTemplateParams params,
  ) async {
    try {
      final preview = await repository.previewTemplate(
        params.templateId,
        params.data,
      );
      return Right(preview);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class ValidateTemplateParams {
  final Template template;

  const ValidateTemplateParams({required this.template});
}

class ValidateTemplate implements UseCase<bool, ValidateTemplateParams> {
  final TemplatesRepository repository;

  ValidateTemplate(this.repository);

  @override
  Future<Either<Failure, bool>> call(ValidateTemplateParams params) async {
    try {
      final isValid = await repository.validateTemplate(params.template);
      return Right(isValid);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class DuplicateTemplateParams {
  final String templateId;
  final String newName;

  const DuplicateTemplateParams({
    required this.templateId,
    required this.newName,
  });
}

class DuplicateTemplate implements UseCase<Template, DuplicateTemplateParams> {
  final TemplatesRepository repository;

  DuplicateTemplate(this.repository);

  @override
  Future<Either<Failure, Template>> call(DuplicateTemplateParams params) async {
    try {
      final duplicatedTemplate = await repository.duplicateTemplate(
        params.templateId,
        params.newName,
      );
      return Right(duplicatedTemplate);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class GetTemplatesByTypeParams {
  final TemplateType type;

  const GetTemplatesByTypeParams({required this.type});
}

class GetTemplatesByType
    implements UseCase<List<Template>, GetTemplatesByTypeParams> {
  final TemplatesRepository repository;

  GetTemplatesByType(this.repository);

  @override
  Future<Either<Failure, List<Template>>> call(
    GetTemplatesByTypeParams params,
  ) async {
    try {
      final templates = await repository.getTemplatesByType(params.type);
      return Right(templates);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
