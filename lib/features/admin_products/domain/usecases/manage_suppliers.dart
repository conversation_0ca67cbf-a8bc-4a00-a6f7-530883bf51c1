import '../entities/supplier.dart';
import '../repositories/admin_products_repository.dart';

class GetSuppliersParams {
  final String? searchQuery;
  final SupplierStatus? status;
  final SupplierType? type;
  final bool? isPreferred;
  final bool? isVerified;
  final String? sortBy;
  final bool sortDescending;
  final int page;
  final int limit;

  const GetSuppliersParams({
    this.searchQuery,
    this.status,
    this.type,
    this.isPreferred,
    this.isVerified,
    this.sortBy,
    this.sortDescending = false,
    this.page = 1,
    this.limit = 20,
  });
}

class GetSuppliers {
  final AdminProductsRepository repository;

  GetSuppliers(this.repository);

  Future<List<Supplier>> call(GetSuppliersParams params) async {
    return await repository.getSuppliers(
      searchQuery: params.searchQuery,
      status: params.status,
      type: params.type,
      isPreferred: params.isPreferred,
      isVerified: params.isVerified,
      sortBy: params.sortBy,
      sortDescending: params.sortDescending,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetSupplierById {
  final AdminProductsRepository repository;

  GetSupplierById(this.repository);

  Future<Supplier?> call(String supplierId) async {
    return await repository.getSupplierById(supplierId);
  }
}

class CreateSupplierParams {
  final String name;
  final String? displayName;
  final String? description;
  final String? supplierCode;
  final SupplierType type;
  final SupplierContact contact;
  final SupplierAddress address;
  final SupplierPaymentTerms paymentTerms;
  final String? taxId;
  final String? registrationNumber;
  final String? logoUrl;
  final List<String> categories;
  final Map<String, dynamic> customFields;
  final List<String> tags;
  final bool isPreferred;
  final DateTime? contractStartDate;
  final DateTime? contractEndDate;

  const CreateSupplierParams({
    required this.name,
    this.displayName,
    this.description,
    this.supplierCode,
    required this.type,
    required this.contact,
    required this.address,
    required this.paymentTerms,
    this.taxId,
    this.registrationNumber,
    this.logoUrl,
    required this.categories,
    required this.customFields,
    required this.tags,
    this.isPreferred = false,
    this.contractStartDate,
    this.contractEndDate,
  });
}

class CreateSupplier {
  final AdminProductsRepository repository;

  CreateSupplier(this.repository);

  Future<Supplier> call(CreateSupplierParams params) async {
    final supplier = Supplier(
      id: '', // Will be generated by the repository
      name: params.name,
      displayName: params.displayName,
      description: params.description,
      supplierCode: params.supplierCode,
      type: params.type,
      status: SupplierStatus.pending,
      contact: params.contact,
      address: params.address,
      paymentTerms: params.paymentTerms,
      taxId: params.taxId,
      registrationNumber: params.registrationNumber,
      logoUrl: params.logoUrl,
      categories: params.categories,
      customFields: params.customFields,
      tags: params.tags,
      isPreferred: params.isPreferred,
      contractStartDate: params.contractStartDate,
      contractEndDate: params.contractEndDate,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: '', // Will be populated by the repository
    );

    return await repository.createSupplier(supplier);
  }
}

class UpdateSupplier {
  final AdminProductsRepository repository;

  UpdateSupplier(this.repository);

  Future<Supplier> call(Supplier supplier) async {
    final updatedSupplier = supplier.copyWith(
      updatedAt: DateTime.now(),
    );
    return await repository.updateSupplier(updatedSupplier);
  }
}

class DeleteSupplier {
  final AdminProductsRepository repository;

  DeleteSupplier(this.repository);

  Future<void> call(String supplierId) async {
    // Check if supplier has active products
    final products = await repository.getProductsBySupplier(supplierId);
    if (products.isNotEmpty) {
      throw Exception('Cannot delete supplier with active products');
    }

    return await repository.deleteSupplier(supplierId);
  }
}

class GetPreferredSuppliers {
  final AdminProductsRepository repository;

  GetPreferredSuppliers(this.repository);

  Future<List<Supplier>> call() async {
    return await repository.getPreferredSuppliers();
  }
}

class GetSuppliersByCategory {
  final AdminProductsRepository repository;

  GetSuppliersByCategory(this.repository);

  Future<List<Supplier>> call(String category) async {
    return await repository.getSuppliersByCategory(category);
  }
}

class GetSupplierAnalytics {
  final AdminProductsRepository repository;

  GetSupplierAnalytics(this.repository);

  Future<Map<String, dynamic>> call() async {
    return await repository.getSupplierAnalytics();
  }
}

class UpdateSupplierStatusParams {
  final String supplierId;
  final SupplierStatus status;
  final String? reason;

  const UpdateSupplierStatusParams({
    required this.supplierId,
    required this.status,
    this.reason,
  });
}

class UpdateSupplierStatus {
  final AdminProductsRepository repository;

  UpdateSupplierStatus(this.repository);

  Future<Supplier> call(UpdateSupplierStatusParams params) async {
    final supplier = await repository.getSupplierById(params.supplierId);
    if (supplier == null) {
      throw Exception('Supplier not found');
    }

    final updatedSupplier = supplier.copyWith(
      status: params.status,
      updatedAt: DateTime.now(),
    );

    return await repository.updateSupplier(updatedSupplier);
  }
}

class UpdateSupplierRatingParams {
  final String supplierId;
  final double rating;
  final String? review;

  const UpdateSupplierRatingParams({
    required this.supplierId,
    required this.rating,
    this.review,
  });
}

class UpdateSupplierRating {
  final AdminProductsRepository repository;

  UpdateSupplierRating(this.repository);

  Future<Supplier> call(UpdateSupplierRatingParams params) async {
    final supplier = await repository.getSupplierById(params.supplierId);
    if (supplier == null) {
      throw Exception('Supplier not found');
    }

    final updatedSupplier = supplier.copyWith(
      rating: params.rating,
      updatedAt: DateTime.now(),
    );

    return await repository.updateSupplier(updatedSupplier);
  }
}

class VerifySupplierParams {
  final String supplierId;
  final bool isVerified;
  final String? verificationNotes;

  const VerifySupplierParams({
    required this.supplierId,
    required this.isVerified,
    this.verificationNotes,
  });
}

class VerifySupplier {
  final AdminProductsRepository repository;

  VerifySupplier(this.repository);

  Future<Supplier> call(VerifySupplierParams params) async {
    final supplier = await repository.getSupplierById(params.supplierId);
    if (supplier == null) {
      throw Exception('Supplier not found');
    }

    final updatedSupplier = supplier.copyWith(
      isVerified: params.isVerified,
      updatedAt: DateTime.now(),
    );

    return await repository.updateSupplier(updatedSupplier);
  }
}

class SetPreferredSupplierParams {
  final String supplierId;
  final bool isPreferred;

  const SetPreferredSupplierParams({
    required this.supplierId,
    required this.isPreferred,
  });
}

class SetPreferredSupplier {
  final AdminProductsRepository repository;

  SetPreferredSupplier(this.repository);

  Future<Supplier> call(SetPreferredSupplierParams params) async {
    final supplier = await repository.getSupplierById(params.supplierId);
    if (supplier == null) {
      throw Exception('Supplier not found');
    }

    final updatedSupplier = supplier.copyWith(
      isPreferred: params.isPreferred,
      updatedAt: DateTime.now(),
    );

    return await repository.updateSupplier(updatedSupplier);
  }
}
