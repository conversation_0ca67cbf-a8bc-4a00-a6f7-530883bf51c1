import '../entities/category.dart';
import '../repositories/admin_products_repository.dart';

class GetCategoriesParams {
  final String? searchQuery;
  final String? parentId;
  final CategoryStatus? status;
  final bool? isFeatured;
  final String? sortBy;
  final bool sortDescending;

  const GetCategoriesParams({
    this.searchQuery,
    this.parentId,
    this.status,
    this.isFeatured,
    this.sortBy,
    this.sortDescending = false,
  });
}

class GetCategories {
  final AdminProductsRepository repository;

  GetCategories(this.repository);

  Future<List<Category>> call(GetCategoriesParams params) async {
    return await repository.getCategories(
      searchQuery: params.searchQuery,
      parentId: params.parentId,
      status: params.status,
      isFeatured: params.isFeatured,
      sortBy: params.sortBy,
      sortDescending: params.sortDescending,
    );
  }
}

class GetCategoryById {
  final AdminProductsRepository repository;

  GetCategoryById(this.repository);

  Future<Category?> call(String categoryId) async {
    return await repository.getCategoryById(categoryId);
  }
}

class CreateCategoryParams {
  final String name;
  final String description;
  final String? shortDescription;
  final String? parentId;
  final String? imageUrl;
  final String? iconUrl;
  final int sortOrder;
  final bool isFeatured;
  final bool showInMenu;
  final CategorySEO seo;
  final Map<String, dynamic> customFields;

  const CreateCategoryParams({
    required this.name,
    required this.description,
    this.shortDescription,
    this.parentId,
    this.imageUrl,
    this.iconUrl,
    this.sortOrder = 0,
    this.isFeatured = false,
    this.showInMenu = true,
    required this.seo,
    required this.customFields,
  });
}

class CreateCategory {
  final AdminProductsRepository repository;

  CreateCategory(this.repository);

  Future<Category> call(CreateCategoryParams params) async {
    // Calculate level based on parent
    int level = 0;
    String? parentName;
    
    if (params.parentId != null) {
      final parent = await repository.getCategoryById(params.parentId!);
      if (parent != null) {
        level = parent.level + 1;
        parentName = parent.name;
      }
    }

    final category = Category(
      id: '', // Will be generated by the repository
      name: params.name,
      description: params.description,
      shortDescription: params.shortDescription,
      parentId: params.parentId,
      parentName: parentName,
      imageUrl: params.imageUrl,
      iconUrl: params.iconUrl,
      status: CategoryStatus.active,
      sortOrder: params.sortOrder,
      isFeatured: params.isFeatured,
      showInMenu: params.showInMenu,
      seo: params.seo,
      customFields: params.customFields,
      level: level,
      childrenIds: const [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: '', // Will be populated by the repository
    );

    return await repository.createCategory(category);
  }
}

class UpdateCategory {
  final AdminProductsRepository repository;

  UpdateCategory(this.repository);

  Future<Category> call(Category category) async {
    final updatedCategory = category.copyWith(
      updatedAt: DateTime.now(),
    );
    return await repository.updateCategory(updatedCategory);
  }
}

class DeleteCategory {
  final AdminProductsRepository repository;

  DeleteCategory(this.repository);

  Future<void> call(String categoryId) async {
    // Check if category has children
    final subcategories = await repository.getSubcategories(categoryId);
    if (subcategories.isNotEmpty) {
      throw Exception('Cannot delete category with subcategories');
    }

    // Check if category has products
    final products = await repository.getProductsByCategory(categoryId);
    if (products.isNotEmpty) {
      throw Exception('Cannot delete category with products');
    }

    return await repository.deleteCategory(categoryId);
  }
}

class GetRootCategories {
  final AdminProductsRepository repository;

  GetRootCategories(this.repository);

  Future<List<Category>> call() async {
    return await repository.getRootCategories();
  }
}

class GetSubcategories {
  final AdminProductsRepository repository;

  GetSubcategories(this.repository);

  Future<List<Category>> call(String parentId) async {
    return await repository.getSubcategories(parentId);
  }
}

class GetFeaturedCategories {
  final AdminProductsRepository repository;

  GetFeaturedCategories(this.repository);

  Future<List<Category>> call() async {
    return await repository.getFeaturedCategories();
  }
}

class UpdateCategoryProductCount {
  final AdminProductsRepository repository;

  UpdateCategoryProductCount(this.repository);

  Future<void> call(String categoryId) async {
    return await repository.updateCategoryProductCount(categoryId);
  }
}

class MoveCategoryParams {
  final String categoryId;
  final String? newParentId;

  const MoveCategoryParams({
    required this.categoryId,
    this.newParentId,
  });
}

class MoveCategory {
  final AdminProductsRepository repository;

  MoveCategory(this.repository);

  Future<Category> call(MoveCategoryParams params) async {
    final category = await repository.getCategoryById(params.categoryId);
    if (category == null) {
      throw Exception('Category not found');
    }

    // Calculate new level and parent name
    int newLevel = 0;
    String? newParentName;
    
    if (params.newParentId != null) {
      final newParent = await repository.getCategoryById(params.newParentId!);
      if (newParent != null) {
        newLevel = newParent.level + 1;
        newParentName = newParent.name;
        
        // Check for circular reference
        if (await _wouldCreateCircularReference(params.categoryId, params.newParentId!)) {
          throw Exception('Cannot move category: would create circular reference');
        }
      }
    }

    final updatedCategory = category.copyWith(
      parentId: params.newParentId,
      parentName: newParentName,
      level: newLevel,
      updatedAt: DateTime.now(),
    );

    return await repository.updateCategory(updatedCategory);
  }

  Future<bool> _wouldCreateCircularReference(String categoryId, String newParentId) async {
    // Check if the new parent is a descendant of the category being moved
    String? currentParentId = newParentId;
    
    while (currentParentId != null) {
      if (currentParentId == categoryId) {
        return true; // Circular reference detected
      }
      
      final parent = await repository.getCategoryById(currentParentId);
      currentParentId = parent?.parentId;
    }
    
    return false;
  }
}

class ReorderCategoriesParams {
  final List<String> categoryIds;
  final String? parentId;

  const ReorderCategoriesParams({
    required this.categoryIds,
    this.parentId,
  });
}

class ReorderCategories {
  final AdminProductsRepository repository;

  ReorderCategories(this.repository);

  Future<void> call(ReorderCategoriesParams params) async {
    for (int i = 0; i < params.categoryIds.length; i++) {
      final categoryId = params.categoryIds[i];
      final category = await repository.getCategoryById(categoryId);
      
      if (category != null) {
        final updatedCategory = category.copyWith(
          sortOrder: i,
          updatedAt: DateTime.now(),
        );
        await repository.updateCategory(updatedCategory);
      }
    }
  }
}
