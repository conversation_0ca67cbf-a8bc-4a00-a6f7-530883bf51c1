import '../entities/admin_product.dart';
import '../repositories/admin_products_repository.dart';

class GetProductsParams {
  final String? searchQuery;
  final String? categoryId;
  final String? supplierId;
  final ProductStatus? status;
  final ProductType? type;
  final bool? isFeatured;
  final bool? isLowStock;
  final String? sortBy;
  final bool sortDescending;
  final int page;
  final int limit;

  const GetProductsParams({
    this.searchQuery,
    this.categoryId,
    this.supplierId,
    this.status,
    this.type,
    this.isFeatured,
    this.isLowStock,
    this.sortBy,
    this.sortDescending = false,
    this.page = 1,
    this.limit = 20,
  });
}

class GetProducts {
  final AdminProductsRepository repository;

  GetProducts(this.repository);

  Future<List<AdminProduct>> call(GetProductsParams params) async {
    return await repository.getProducts(
      searchQuery: params.searchQuery,
      categoryId: params.categoryId,
      supplierId: params.supplierId,
      status: params.status,
      type: params.type,
      isFeatured: params.isFeatured,
      isLowStock: params.isLowStock,
      sortBy: params.sortBy,
      sortDescending: params.sortDescending,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetProductById {
  final AdminProductsRepository repository;

  GetProductById(this.repository);

  Future<AdminProduct?> call(String productId) async {
    return await repository.getProductById(productId);
  }
}

class CreateProductParams {
  final String name;
  final String description;
  final String? shortDescription;
  final String sku;
  final String categoryId;
  final String? supplierId;
  final ProductType type;
  final double price;
  final double? compareAtPrice;
  final double costPrice;
  final String currency;
  final int stockQuantity;
  final int? lowStockThreshold;
  final bool trackQuantity;
  final List<String> imageUrls;
  final List<ProductVariant> variants;
  final List<String> tags;
  final ProductSEO seo;
  final ProductShipping shipping;
  final Map<String, dynamic> customFields;
  final bool isFeatured;
  final bool isDigital;

  const CreateProductParams({
    required this.name,
    required this.description,
    this.shortDescription,
    required this.sku,
    required this.categoryId,
    this.supplierId,
    required this.type,
    required this.price,
    this.compareAtPrice,
    required this.costPrice,
    required this.currency,
    required this.stockQuantity,
    this.lowStockThreshold,
    this.trackQuantity = true,
    required this.imageUrls,
    required this.variants,
    required this.tags,
    required this.seo,
    required this.shipping,
    required this.customFields,
    this.isFeatured = false,
    this.isDigital = false,
  });
}

class CreateProduct {
  final AdminProductsRepository repository;

  CreateProduct(this.repository);

  Future<AdminProduct> call(CreateProductParams params) async {
    final product = AdminProduct(
      id: '', // Will be generated by the repository
      name: params.name,
      description: params.description,
      shortDescription: params.shortDescription,
      sku: params.sku,
      categoryId: params.categoryId,
      categoryName: '', // Will be populated by the repository
      supplierId: params.supplierId,
      supplierName: null, // Will be populated by the repository
      type: params.type,
      status: ProductStatus.draft,
      price: params.price,
      compareAtPrice: params.compareAtPrice,
      costPrice: params.costPrice,
      currency: params.currency,
      stockQuantity: params.stockQuantity,
      lowStockThreshold: params.lowStockThreshold,
      trackQuantity: params.trackQuantity,
      imageUrls: params.imageUrls,
      variants: params.variants,
      tags: params.tags,
      seo: params.seo,
      shipping: params.shipping,
      customFields: params.customFields,
      isFeatured: params.isFeatured,
      isDigital: params.isDigital,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: '', // Will be populated by the repository
    );

    return await repository.createProduct(product);
  }
}

class UpdateProduct {
  final AdminProductsRepository repository;

  UpdateProduct(this.repository);

  Future<AdminProduct> call(AdminProduct product) async {
    final updatedProduct = product.copyWith(
      updatedAt: DateTime.now(),
    );
    return await repository.updateProduct(updatedProduct);
  }
}

class DeleteProduct {
  final AdminProductsRepository repository;

  DeleteProduct(this.repository);

  Future<void> call(String productId) async {
    return await repository.deleteProduct(productId);
  }
}

class UpdateProductStatus {
  final AdminProductsRepository repository;

  UpdateProductStatus(this.repository);

  Future<void> call(String productId, ProductStatus status) async {
    return await repository.updateProductStatus(productId, status);
  }
}

class UpdateProductStock {
  final AdminProductsRepository repository;

  UpdateProductStock(this.repository);

  Future<void> call(String productId, int quantity) async {
    return await repository.updateProductStock(productId, quantity);
  }
}

class GetProductsByCategory {
  final AdminProductsRepository repository;

  GetProductsByCategory(this.repository);

  Future<List<AdminProduct>> call(String categoryId) async {
    return await repository.getProductsByCategory(categoryId);
  }
}

class GetProductsBySupplier {
  final AdminProductsRepository repository;

  GetProductsBySupplier(this.repository);

  Future<List<AdminProduct>> call(String supplierId) async {
    return await repository.getProductsBySupplier(supplierId);
  }
}

class GetLowStockProducts {
  final AdminProductsRepository repository;

  GetLowStockProducts(this.repository);

  Future<List<AdminProduct>> call() async {
    return await repository.getLowStockProducts();
  }
}

class GetFeaturedProducts {
  final AdminProductsRepository repository;

  GetFeaturedProducts(this.repository);

  Future<List<AdminProduct>> call() async {
    return await repository.getFeaturedProducts();
  }
}

class GetProductsAnalytics {
  final AdminProductsRepository repository;

  GetProductsAnalytics(this.repository);

  Future<Map<String, dynamic>> call() async {
    return await repository.getProductsAnalytics();
  }
}

class SearchProducts {
  final AdminProductsRepository repository;

  SearchProducts(this.repository);

  Future<List<AdminProduct>> call(String query, {int limit = 10}) async {
    return await repository.searchProducts(query, limit: limit);
  }
}

class BulkUpdateProductsParams {
  final List<String> productIds;
  final ProductStatus? status;
  final String? categoryId;
  final String? supplierId;

  const BulkUpdateProductsParams({
    required this.productIds,
    this.status,
    this.categoryId,
    this.supplierId,
  });
}

class BulkUpdateProducts {
  final AdminProductsRepository repository;

  BulkUpdateProducts(this.repository);

  Future<void> call(BulkUpdateProductsParams params) async {
    if (params.status != null) {
      await repository.bulkUpdateProductStatus(params.productIds, params.status!);
    }
    if (params.categoryId != null) {
      await repository.bulkUpdateProductCategory(params.productIds, params.categoryId!);
    }
    if (params.supplierId != null) {
      await repository.bulkUpdateProductSupplier(params.productIds, params.supplierId!);
    }
  }
}

class ExportProductsParams {
  final String format;
  final List<String>? productIds;
  final Map<String, dynamic>? filters;

  const ExportProductsParams({
    this.format = 'csv',
    this.productIds,
    this.filters,
  });
}

class ExportProducts {
  final AdminProductsRepository repository;

  ExportProducts(this.repository);

  Future<String> call(ExportProductsParams params) async {
    return await repository.exportProducts(
      format: params.format,
      productIds: params.productIds,
      filters: params.filters,
    );
  }
}

class ImportProducts {
  final AdminProductsRepository repository;

  ImportProducts(this.repository);

  Future<Map<String, dynamic>> call(String fileUrl, {bool validateOnly = false}) async {
    return await repository.importProducts(fileUrl, validateOnly: validateOnly);
  }
}
