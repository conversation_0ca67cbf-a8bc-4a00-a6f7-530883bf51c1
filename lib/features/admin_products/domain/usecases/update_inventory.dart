import '../entities/inventory.dart';
import '../repositories/admin_products_repository.dart';

class GetInventoryParams {
  final String? searchQuery;
  final String? productId;
  final String? locationId;
  final InventoryStatus? status;
  final bool? isLowStock;
  final int page;
  final int limit;

  const GetInventoryParams({
    this.searchQuery,
    this.productId,
    this.locationId,
    this.status,
    this.isLowStock,
    this.page = 1,
    this.limit = 20,
  });
}

class GetInventory {
  final AdminProductsRepository repository;

  GetInventory(this.repository);

  Future<List<Inventory>> call(GetInventoryParams params) async {
    return await repository.getInventory(
      searchQuery: params.searchQuery,
      productId: params.productId,
      locationId: params.locationId,
      status: params.status,
      isLowStock: params.isLowStock,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetInventoryById {
  final AdminProductsRepository repository;

  GetInventoryById(this.repository);

  Future<Inventory?> call(String inventoryId) async {
    return await repository.getInventoryById(inventoryId);
  }
}

class GetInventoryByProductParams {
  final String productId;
  final String? variantId;
  final String? locationId;

  const GetInventoryByProductParams({
    required this.productId,
    this.variantId,
    this.locationId,
  });
}

class GetInventoryByProduct {
  final AdminProductsRepository repository;

  GetInventoryByProduct(this.repository);

  Future<Inventory?> call(GetInventoryByProductParams params) async {
    return await repository.getInventoryByProduct(
      params.productId,
      variantId: params.variantId,
      locationId: params.locationId,
    );
  }
}

class UpdateInventoryParams {
  final String inventoryId;
  final int? quantity;
  final int? reservedQuantity;
  final int? reorderPoint;
  final int? maxStockLevel;
  final double? averageCost;
  final InventoryStatus? status;

  const UpdateInventoryParams({
    required this.inventoryId,
    this.quantity,
    this.reservedQuantity,
    this.reorderPoint,
    this.maxStockLevel,
    this.averageCost,
    this.status,
  });
}

class UpdateInventoryUseCase {
  final AdminProductsRepository repository;

  UpdateInventoryUseCase(this.repository);

  Future<Inventory> call(UpdateInventoryParams params) async {
    final currentInventory = await repository.getInventoryById(
      params.inventoryId,
    );
    if (currentInventory == null) {
      throw Exception('Inventory not found');
    }

    final updatedInventory = currentInventory.copyWith(
      quantity: params.quantity,
      reservedQuantity: params.reservedQuantity,
      availableQuantity: params.quantity != null
          ? (params.quantity! -
                (params.reservedQuantity ?? currentInventory.reservedQuantity))
          : null,
      reorderPoint: params.reorderPoint,
      maxStockLevel: params.maxStockLevel,
      averageCost: params.averageCost,
      totalValue: params.averageCost != null && params.quantity != null
          ? params.averageCost! * params.quantity!
          : null,
      status: params.status,
      updatedAt: DateTime.now(),
    );

    return await repository.updateInventory(updatedInventory);
  }
}

class AddInventoryMovementParams {
  final String productId;
  final String? variantId;
  final InventoryMovementType type;
  final int quantity;
  final String reason;
  final String? reference;
  final double? unitCost;
  final String? notes;
  final String performedBy;
  final String performedByName;

  const AddInventoryMovementParams({
    required this.productId,
    this.variantId,
    required this.type,
    required this.quantity,
    required this.reason,
    this.reference,
    this.unitCost,
    this.notes,
    required this.performedBy,
    required this.performedByName,
  });
}

class AddInventoryMovement {
  final AdminProductsRepository repository;

  AddInventoryMovement(this.repository);

  Future<InventoryMovement> call(AddInventoryMovementParams params) async {
    // Get current inventory to calculate previous and new quantities
    final currentInventory = await repository.getInventoryByProduct(
      params.productId,
      variantId: params.variantId,
    );

    final previousQuantity = currentInventory?.quantity ?? 0;
    final newQuantity = _calculateNewQuantity(
      previousQuantity,
      params.quantity,
      params.type,
    );

    final movement = InventoryMovement(
      id: '', // Will be generated by the repository
      productId: params.productId,
      variantId: params.variantId,
      type: params.type,
      quantity: params.quantity,
      previousQuantity: previousQuantity,
      newQuantity: newQuantity,
      reason: params.reason,
      reference: params.reference,
      unitCost: params.unitCost,
      notes: params.notes,
      performedBy: params.performedBy,
      performedByName: params.performedByName,
      timestamp: DateTime.now(),
    );

    return await repository.addInventoryMovement(movement);
  }

  int _calculateNewQuantity(
    int previousQuantity,
    int movementQuantity,
    InventoryMovementType type,
  ) {
    switch (type) {
      case InventoryMovementType.stockIn:
      case InventoryMovementType.returned:
        return previousQuantity + movementQuantity;
      case InventoryMovementType.stockOut:
      case InventoryMovementType.sale:
      case InventoryMovementType.damaged:
      case InventoryMovementType.expired:
        return previousQuantity - movementQuantity;
      case InventoryMovementType.adjustment:
        return movementQuantity; // Adjustment sets absolute quantity
      case InventoryMovementType.transfer:
        return previousQuantity - movementQuantity; // Transfer out
    }
  }
}

class GetInventoryMovementsParams {
  final String? productId;
  final String? locationId;
  final InventoryMovementType? type;
  final DateTime? startDate;
  final DateTime? endDate;
  final int page;
  final int limit;

  const GetInventoryMovementsParams({
    this.productId,
    this.locationId,
    this.type,
    this.startDate,
    this.endDate,
    this.page = 1,
    this.limit = 50,
  });
}

class GetInventoryMovements {
  final AdminProductsRepository repository;

  GetInventoryMovements(this.repository);

  Future<List<InventoryMovement>> call(
    GetInventoryMovementsParams params,
  ) async {
    return await repository.getInventoryMovements(
      productId: params.productId,
      locationId: params.locationId,
      type: params.type,
      startDate: params.startDate,
      endDate: params.endDate,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetLowStockInventory {
  final AdminProductsRepository repository;

  GetLowStockInventory(this.repository);

  Future<List<Inventory>> call() async {
    return await repository.getLowStockInventory();
  }
}

class GetInventoryLocations {
  final AdminProductsRepository repository;

  GetInventoryLocations(this.repository);

  Future<List<InventoryLocation>> call() async {
    return await repository.getInventoryLocations();
  }
}

class GetInventoryAnalytics {
  final AdminProductsRepository repository;

  GetInventoryAnalytics(this.repository);

  Future<Map<String, dynamic>> call() async {
    return await repository.getInventoryAnalytics();
  }
}

class ExportInventoryParams {
  final String format;
  final String? locationId;
  final Map<String, dynamic>? filters;

  const ExportInventoryParams({
    this.format = 'csv',
    this.locationId,
    this.filters,
  });
}

class ExportInventory {
  final AdminProductsRepository repository;

  ExportInventory(this.repository);

  Future<String> call(ExportInventoryParams params) async {
    return await repository.exportInventory(
      format: params.format,
      locationId: params.locationId,
      filters: params.filters,
    );
  }
}
