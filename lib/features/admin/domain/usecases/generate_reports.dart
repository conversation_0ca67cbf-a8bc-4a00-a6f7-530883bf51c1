import '../entities/report.dart';
import '../repositories/admin_repository.dart';

class ViewReportsParams {
  final ReportType? type;
  final ReportStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int page;
  final int limit;

  const ViewReportsParams({
    this.type,
    this.status,
    this.startDate,
    this.endDate,
    this.page = 1,
    this.limit = 20,
  });
}

class ViewReports {
  final AdminRepository repository;

  ViewReports(this.repository);

  Future<List<Report>> call(ViewReportsParams params) async {
    return await repository.getReports(
      type: params.type,
      status: params.status,
      startDate: params.startDate,
      endDate: params.endDate,
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetReportDetails {
  final AdminRepository repository;

  GetReportDetails(this.repository);

  Future<Report?> call(String reportId) async {
    return await repository.getReportById(reportId);
  }
}

class CreateReportParams {
  final String name;
  final String description;
  final ReportType type;
  final ReportFormat format;
  final DateTime startDate;
  final DateTime endDate;
  final List<ReportFilter> filters;
  final Map<String, dynamic> parameters;
  final String? templateId;

  const CreateReportParams({
    required this.name,
    required this.description,
    required this.type,
    required this.format,
    required this.startDate,
    required this.endDate,
    required this.filters,
    required this.parameters,
    this.templateId,
  });
}

class CreateReport {
  final AdminRepository repository;

  CreateReport(this.repository);

  Future<Report> call(CreateReportParams params) async {
    final report = Report(
      id: '', // Will be generated by the repository
      name: params.name,
      description: params.description,
      type: params.type,
      format: params.format,
      status: ReportStatus.pending,
      templateId: params.templateId,
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.filters,
      sections: const [], // Will be populated during generation
      parameters: params.parameters,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.createReport(report);
  }
}

class GenerateReport {
  final AdminRepository repository;

  GenerateReport(this.repository);

  Future<String> call(String reportId) async {
    return await repository.generateReport(reportId);
  }
}

class UpdateReport {
  final AdminRepository repository;

  UpdateReport(this.repository);

  Future<Report> call(Report report) async {
    return await repository.updateReport(report);
  }
}

class DeleteReport {
  final AdminRepository repository;

  DeleteReport(this.repository);

  Future<void> call(String reportId) async {
    return await repository.deleteReport(reportId);
  }
}

class GetScheduledReports {
  final AdminRepository repository;

  GetScheduledReports(this.repository);

  Future<List<Report>> call() async {
    return await repository.getScheduledReports();
  }
}

class ScheduleReportParams {
  final Report report;
  final ReportSchedule schedule;

  const ScheduleReportParams({
    required this.report,
    required this.schedule,
  });
}

class ScheduleReport {
  final AdminRepository repository;

  ScheduleReport(this.repository);

  Future<Report> call(ScheduleReportParams params) async {
    return await repository.scheduleReport(params.report, params.schedule);
  }
}

class ExportDataParams {
  final String dataType;
  final String format;
  final DateTime? startDate;
  final DateTime? endDate;
  final Map<String, dynamic>? filters;

  const ExportDataParams({
    required this.dataType,
    required this.format,
    this.startDate,
    this.endDate,
    this.filters,
  });
}

class ExportData {
  final AdminRepository repository;

  ExportData(this.repository);

  Future<String> call(ExportDataParams params) async {
    return await repository.exportData(
      dataType: params.dataType,
      format: params.format,
      startDate: params.startDate,
      endDate: params.endDate,
      filters: params.filters,
    );
  }
}
